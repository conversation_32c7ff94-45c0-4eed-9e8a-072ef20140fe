"""
图形用户界面模块
使用tkinter创建用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from typing import Callable, Optional


class MainGUI:
    """主界面类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("键盘鼠标动作录制和回放工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_recording = False
        self.is_playing = False
        self.current_actions = []
        
        # 回调函数
        self.on_start_recording: Optional[Callable] = None
        self.on_stop_recording: Optional[Callable] = None
        self.on_start_playback: Optional[Callable] = None
        self.on_stop_playback: Optional[Callable] = None
        self.on_save_actions: Optional[Callable] = None
        self.on_load_actions: Optional[Callable] = None
        self.on_save_as_actions: Optional[Callable] = None
        
        self._create_widgets()
        self._setup_layout()
        
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="键盘鼠标动作录制和回放工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 录制控制区域
        record_frame = ttk.LabelFrame(main_frame, text="录制控制", padding="10")
        record_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.record_button = ttk.Button(record_frame, text="开始录制", 
                                       command=self._on_record_click)
        self.record_button.grid(row=0, column=0, padx=(0, 10))
        
        self.record_status_label = ttk.Label(record_frame, text="状态: 待机", 
                                           foreground="blue")
        self.record_status_label.grid(row=0, column=1, padx=(10, 0))
        
        # 回放控制区域
        playback_frame = ttk.LabelFrame(main_frame, text="回放控制", padding="10")
        playback_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 播放次数设置
        ttk.Label(playback_frame, text="播放次数:").grid(row=0, column=0, sticky=tk.W)
        
        self.repeat_var = tk.StringVar(value="1")
        repeat_frame = ttk.Frame(playback_frame)
        repeat_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        self.repeat_entry = ttk.Entry(repeat_frame, textvariable=self.repeat_var, width=10)
        self.repeat_entry.grid(row=0, column=0, padx=(0, 5))
        
        self.infinite_var = tk.BooleanVar()
        self.infinite_check = ttk.Checkbutton(repeat_frame, text="无限循环", 
                                            variable=self.infinite_var,
                                            command=self._on_infinite_toggle)
        self.infinite_check.grid(row=0, column=1)
        
        # 回放按钮
        self.play_button = ttk.Button(playback_frame, text="开始回放", 
                                     command=self._on_play_click, state="disabled")
        self.play_button.grid(row=1, column=0, pady=(10, 0))
        
        self.play_status_label = ttk.Label(playback_frame, text="状态: 无录制数据", 
                                         foreground="gray")
        self.play_status_label.grid(row=1, column=1, padx=(10, 0), pady=(10, 0))
        
        # 文件操作区域
        file_frame = ttk.LabelFrame(main_frame, text="文件操作", padding="10")
        file_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.save_button = ttk.Button(file_frame, text="保存录制", 
                                     command=self._on_save_click, state="disabled")
        self.save_button.grid(row=0, column=0, padx=(0, 5))
        
        self.save_as_button = ttk.Button(file_frame, text="另存为", 
                                        command=self._on_save_as_click, state="disabled")
        self.save_as_button.grid(row=0, column=1, padx=(0, 5))
        
        self.load_button = ttk.Button(file_frame, text="加载录制", 
                                     command=self._on_load_click)
        self.load_button.grid(row=0, column=2)
        
        # 信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="录制信息", padding="10")
        info_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.info_text = scrolledtext.ScrolledText(info_frame, height=8, width=60)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        record_frame.columnconfigure(1, weight=1)
        playback_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(2, weight=1)
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
        repeat_frame.columnconfigure(1, weight=1)
        
    def _setup_layout(self):
        """设置布局"""
        self._update_info("程序已启动，请开始录制或加载已有的录制文件。")
        
    def _on_record_click(self):
        """录制按钮点击事件"""
        if not self.is_recording:
            # 开始录制
            if self.on_start_recording:
                success = self.on_start_recording()
                if success:
                    self.is_recording = True
                    self.record_button.config(text="停止录制")
                    self.record_status_label.config(text="状态: 录制中", foreground="red")
                    self._update_info("开始录制...")
        else:
            # 停止录制
            if self.on_stop_recording:
                success = self.on_stop_recording()
                if success:
                    self.is_recording = False
                    self.record_button.config(text="开始录制")
                    self.record_status_label.config(text="状态: 录制完成", foreground="green")
                    self._update_info("录制完成！")
                    self._enable_playback_controls()
    
    def _on_play_click(self):
        """回放按钮点击事件"""
        if not self.is_playing:
            # 开始回放
            try:
                if self.infinite_var.get():
                    repeat_count = -1
                else:
                    repeat_count = int(self.repeat_var.get())
                    if repeat_count <= 0:
                        messagebox.showerror("错误", "播放次数必须大于0！")
                        return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的播放次数！")
                return
                
            if self.on_start_playback:
                success = self.on_start_playback(repeat_count)
                if success:
                    self.is_playing = True
                    self.play_button.config(text="停止回放")
                    self.play_status_label.config(text="状态: 回放中", foreground="red")
                    self._update_info(f"开始回放，重复次数: {'无限' if repeat_count == -1 else repeat_count}")
        else:
            # 停止回放
            if self.on_stop_playback:
                success = self.on_stop_playback()
                if success:
                    self.is_playing = False
                    self.play_button.config(text="开始回放")
                    self.play_status_label.config(text="状态: 已停止", foreground="orange")
                    self._update_info("回放已停止")
    
    def _on_infinite_toggle(self):
        """无限循环复选框切换事件"""
        if self.infinite_var.get():
            self.repeat_entry.config(state="disabled")
        else:
            self.repeat_entry.config(state="normal")
    
    def _on_save_click(self):
        """保存按钮点击事件"""
        if self.on_save_actions:
            self.on_save_actions()
    
    def _on_save_as_click(self):
        """另存为按钮点击事件"""
        if self.on_save_as_actions:
            self.on_save_as_actions()
    
    def _on_load_click(self):
        """加载按钮点击事件"""
        if self.on_load_actions:
            self.on_load_actions()
    
    def _enable_playback_controls(self):
        """启用回放控制"""
        self.play_button.config(state="normal")
        self.play_status_label.config(text="状态: 准备就绪", foreground="blue")
        self.save_button.config(state="normal")
        self.save_as_button.config(state="normal")
    
    def _update_info(self, message: str):
        """更新信息显示"""
        self.info_text.insert(tk.END, f"{message}\n")
        self.info_text.see(tk.END)
    
    def on_playback_complete(self):
        """回放完成回调"""
        self.is_playing = False
        self.play_button.config(text="开始回放")
        self.play_status_label.config(text="状态: 回放完成", foreground="green")
        self._update_info("回放完成！")
    
    def set_actions_loaded(self, action_count: int):
        """设置动作已加载状态"""
        self._enable_playback_controls()
        self._update_info(f"已加载录制文件，包含 {action_count} 个动作")
    
    def run(self):
        """运行主循环"""
        self.root.mainloop()
