# 键盘鼠标动作录制和回放工具 - 使用说明

## 📋 项目简介

这是一个基于Python开发的键盘鼠标动作录制和回放工具，可以精确录制用户的键盘按键和鼠标操作，并能够按照原始时序准确地回放这些动作。

## ✨ 功能特性

### 录制功能
- 🎯 **键盘事件录制**: 捕获所有按键的按下和释放事件
- 🖱️ **鼠标点击录制**: 支持左键、右键、中键点击事件
- 📍 **鼠标移动录制**: 记录鼠标移动轨迹
- 🎡 **滚轮事件录制**: 捕获鼠标滚轮的上下滚动
- ⏰ **精确时间戳**: 记录每个动作的精确时间，确保回放时序准确

### 回放功能
- 🔄 **精确回放**: 按照录制时的时序准确重现所有动作
- 🔢 **次数控制**: 支持设置播放次数或无限循环播放
- ⏸️ **实时控制**: 支持随时开始/停止回放

### 数据管理
- 💾 **数据保存**: 将录制数据保存为JSON格式文件
- 📂 **文件管理**: 支持加载已保存的录制文件
- 📋 **另存为**: 支持将录制数据保存到指定位置

### 用户界面
- 🎮 **简洁界面**: 基于tkinter的直观图形界面
- 📊 **状态显示**: 实时显示录制/回放状态
- 📝 **信息反馈**: 详细的操作信息和错误提示

## 🚀 快速开始

### 方法一：运行可执行文件（推荐）
1. 直接双击运行 `MouseKeyboardRecorder.exe`
2. 无需安装Python环境和依赖包

### 方法二：从源码运行
1. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```
   或者双击运行 `install_requirements.bat`

2. **运行程序**
   ```bash
   python main.py
   ```
   或者双击运行 `run_program.bat`

## 📖 详细使用教程

### 1. 录制动作
1. 启动程序后，点击 **"开始录制"** 按钮
2. 状态显示变为 "录制中"（红色）
3. 执行需要录制的键盘鼠标操作
4. 完成后点击 **"停止录制"** 按钮
5. 状态显示变为 "录制完成"（绿色）

### 2. 设置回放参数
- **播放次数**: 在输入框中输入想要重复的次数
- **无限循环**: 勾选此选项可以无限循环播放
- 注意：勾选无限循环后，播放次数输入框会被禁用

### 3. 回放动作
1. 确保已有录制数据（刚录制完成或已加载文件）
2. 设置好播放次数
3. 点击 **"开始回放"** 按钮
4. 程序会按照录制时的时序重现所有动作
5. 可以随时点击 **"停止回放"** 按钮中止播放

### 4. 保存和加载
- **保存录制**: 点击 "保存录制" 按钮，自动保存到 `recordings` 文件夹
- **另存为**: 点击 "另存为" 按钮，选择保存位置和文件名
- **加载录制**: 点击 "加载录制" 按钮，选择要加载的录制文件

## 🛠️ 技术规格

### 系统要求
- **操作系统**: Windows 7/8/10/11
- **Python版本**: 3.6+ （源码运行时需要）
- **内存**: 最少 100MB 可用内存

### 技术栈
- **Python 3.8.10**: 主要开发语言
- **pynput**: 键盘鼠标事件处理库
- **tkinter**: 图形用户界面框架
- **PyInstaller**: 可执行文件打包工具

### 文件格式
录制数据以JSON格式保存，包含以下信息：
```json
{
  "version": "1.0",
  "created_time": "2024-01-01T12:00:00",
  "action_count": 100,
  "total_duration": 10.5,
  "actions": [
    {
      "type": "mouse_move",
      "timestamp": 0.0,
      "x": 100,
      "y": 200
    },
    {
      "type": "mouse_click",
      "timestamp": 0.1,
      "x": 100,
      "y": 200,
      "button": "left",
      "pressed": true
    }
  ]
}
```

## 🔧 开发和构建

### 测试程序
```bash
python test_modules.py
```

### 构建可执行文件
```bash
python build.py
```
或者双击运行 `build_exe.bat`

### 项目结构
```
鼠标滚轮/
├── main.py              # 主程序入口
├── recorder.py          # 录制模块
├── player.py            # 回放模块
├── data_manager.py      # 数据存储管理
├── gui.py               # 图形用户界面
├── build.py             # 打包脚本
├── test_modules.py      # 测试脚本
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
├── 使用说明.md         # 详细使用说明
├── recordings/         # 录制文件存储目录
├── *.bat               # 便捷批处理文件
└── dist/               # 构建输出目录
    └── MouseKeyboardRecorder.exe
```

## ⚠️ 注意事项

### 使用限制
1. **管理员权限**: 某些系统操作可能需要管理员权限
2. **安全软件**: 部分杀毒软件可能会误报，请添加到白名单
3. **系统兼容性**: 主要针对Windows系统优化

### 最佳实践
1. **录制前准备**: 确保录制环境与回放环境相似
2. **适度录制**: 避免录制过长的操作序列
3. **定期保存**: 重要的录制数据及时保存
4. **测试回放**: 录制完成后先测试回放效果

### 故障排除
1. **程序无法启动**: 检查Python环境和依赖包
2. **录制无效果**: 确认程序有足够的系统权限
3. **回放不准确**: 检查屏幕分辨率和窗口位置是否一致
4. **文件无法保存**: 确认有足够的磁盘空间和写入权限

## 📞 技术支持

如果遇到问题或有改进建议，请：
1. 检查本使用说明中的故障排除部分
2. 运行 `test_modules.py` 检查模块状态
3. 查看程序输出的错误信息

## 📄 许可证

本项目仅供学习和个人使用，请遵守相关法律法规。
