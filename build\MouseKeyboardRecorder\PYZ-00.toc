('C:\\Users\\<USER>\\Desktop\\xiangmu\\鼠标滚轮\\build\\MouseKeyboardRecorder\\PYZ-00.pyz',
 [('_compat_pickle', 'D:\\Python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Python\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\Python\\lib\\argparse.py', 'PYMODULE'),
  ('base64', 'D:\\Python\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\lib\\calendar.py', 'PYMODULE'),
  ('configparser', 'D:\\Python\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\lib\\contextlib.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Python\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('data_manager',
   'C:\\Users\\<USER>\\Desktop\\xiangmu\\鼠标滚轮\\data_manager.py',
   'PYMODULE'),
  ('datetime', 'D:\\Python\\lib\\datetime.py', 'PYMODULE'),
  ('email', 'D:\\Python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\Python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python\\lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python\\lib\\fnmatch.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\lib\\gettext.py', 'PYMODULE'),
  ('gui', 'C:\\Users\\<USER>\\Desktop\\xiangmu\\鼠标滚轮\\gui.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\Python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata', 'D:\\Python\\lib\\importlib\\metadata.py', 'PYMODULE'),
  ('importlib.util', 'D:\\Python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('json', 'D:\\Python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\Python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\lib\\lzma.py', 'PYMODULE'),
  ('optparse', 'D:\\Python\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\lib\\pickle.py', 'PYMODULE'),
  ('player', 'C:\\Users\\<USER>\\Desktop\\xiangmu\\鼠标滚轮\\player.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python\\lib\\random.py', 'PYMODULE'),
  ('recorder',
   'C:\\Users\\<USER>\\Desktop\\xiangmu\\鼠标滚轮\\recorder.py',
   'PYMODULE'),
  ('selectors', 'D:\\Python\\lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Python\\lib\\socket.py', 'PYMODULE'),
  ('string', 'D:\\Python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\Python\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'D:\\Python\\lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'D:\\Python\\lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'D:\\Python\\lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.messagebox', 'D:\\Python\\lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\Python\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Python\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\Python\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\Python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\Python\\lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\Python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('uu', 'D:\\Python\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python\\lib\\zipfile.py', 'PYMODULE')])
