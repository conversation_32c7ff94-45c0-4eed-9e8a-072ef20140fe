"""
模块测试脚本
用于测试各个模块的基本功能
"""

import sys
import time
import json
from datetime import datetime

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    try:
        from recorder import ActionRecorder
        from player import ActionPlayer
        from data_manager import DataManager
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_recorder():
    """测试录制器基本功能"""
    print("\n测试录制器...")
    try:
        from recorder import ActionRecorder
        
        recorder = ActionRecorder()
        
        # 测试初始状态
        assert not recorder.is_recording, "初始状态应该是未录制"
        assert len(recorder.get_actions()) == 0, "初始动作列表应该为空"
        
        # 测试开始录制
        success = recorder.start_recording()
        assert success, "开始录制应该成功"
        assert recorder.is_recording, "录制状态应该为True"
        
        # 模拟短暂录制
        time.sleep(0.1)
        
        # 测试停止录制
        success = recorder.stop_recording()
        assert success, "停止录制应该成功"
        assert not recorder.is_recording, "录制状态应该为False"
        
        print("✓ 录制器测试通过")
        return True
    except Exception as e:
        print(f"✗ 录制器测试失败: {e}")
        return False

def test_player():
    """测试播放器基本功能"""
    print("\n测试播放器...")
    try:
        from player import ActionPlayer
        
        player = ActionPlayer()
        
        # 测试初始状态
        assert not player.is_playing, "初始状态应该是未播放"
        
        # 测试空动作列表
        success = player.start_playback(1)
        assert not success, "空动作列表应该无法开始播放"
        
        # 设置测试动作
        test_actions = [
            {
                'type': 'mouse_move',
                'timestamp': 0.0,
                'x': 100,
                'y': 100
            },
            {
                'type': 'mouse_click',
                'timestamp': 0.1,
                'x': 100,
                'y': 100,
                'button': 'left',
                'pressed': True
            }
        ]
        
        player.set_actions(test_actions)
        
        print("✓ 播放器测试通过")
        return True
    except Exception as e:
        print(f"✗ 播放器测试失败: {e}")
        return False

def test_data_manager():
    """测试数据管理器基本功能"""
    print("\n测试数据管理器...")
    try:
        from data_manager import DataManager
        
        data_manager = DataManager()
        
        # 测试文件夹创建
        import os
        assert os.path.exists(data_manager.default_folder), "录制文件夹应该存在"
        
        # 测试保存功能（使用测试数据）
        test_actions = [
            {
                'type': 'mouse_move',
                'timestamp': 0.0,
                'x': 100,
                'y': 100
            }
        ]
        
        # 创建测试文件名
        test_filename = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        print("✓ 数据管理器测试通过")
        return True
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {e}")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n测试GUI模块...")
    try:
        import tkinter as tk
        from gui import MainGUI
        
        # 只测试导入，不创建实际窗口
        print("✓ GUI模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("键盘鼠标录制工具 - 模块测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_recorder,
        test_player,
        test_data_manager,
        test_gui_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！程序应该可以正常运行。")
        print("\n运行主程序: python main.py")
    else:
        print("✗ 部分测试失败，请检查依赖包是否正确安装。")
        print("\n安装依赖: pip install -r requirements.txt")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
