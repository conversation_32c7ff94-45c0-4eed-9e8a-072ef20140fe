"""
数据存储管理模块
负责录制数据的保存和加载
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from tkinter import filedialog, messagebox


class DataManager:
    """数据管理器类"""
    
    def __init__(self):
        self.default_folder = "recordings"
        self._ensure_folder_exists()
    
    def _ensure_folder_exists(self):
        """确保录制文件夹存在"""
        if not os.path.exists(self.default_folder):
            os.makedirs(self.default_folder)
    
    def save_actions(self, actions: List[Dict[str, Any]], filename: str = None) -> bool:
        """
        保存动作数据到JSON文件
        :param actions: 动作列表
        :param filename: 文件名，如果为None则自动生成
        :return: 保存是否成功
        """
        try:
            if not actions:
                messagebox.showwarning("警告", "没有可保存的录制数据！")
                return False
            
            # 如果没有指定文件名，则自动生成
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"recording_{timestamp}.json"
            
            # 确保文件名以.json结尾
            if not filename.endswith('.json'):
                filename += '.json'
            
            # 构建完整路径
            filepath = os.path.join(self.default_folder, filename)
            
            # 准备保存的数据
            save_data = {
                'version': '1.0',
                'created_time': datetime.now().isoformat(),
                'action_count': len(actions),
                'total_duration': actions[-1]['timestamp'] if actions else 0,
                'actions': actions
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            print(f"录制数据已保存到: {filepath}")
            messagebox.showinfo("成功", f"录制数据已保存到:\n{filepath}")
            return True
            
        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def load_actions(self, filepath: str = None) -> Optional[List[Dict[str, Any]]]:
        """
        从JSON文件加载动作数据
        :param filepath: 文件路径，如果为None则弹出文件选择对话框
        :return: 动作列表，加载失败返回None
        """
        try:
            # 如果没有指定文件路径，则弹出文件选择对话框
            if filepath is None:
                filepath = filedialog.askopenfilename(
                    title="选择录制文件",
                    initialdir=self.default_folder,
                    filetypes=[
                        ("JSON文件", "*.json"),
                        ("所有文件", "*.*")
                    ]
                )
                
                if not filepath:  # 用户取消了选择
                    return None
            
            # 检查文件是否存在
            if not os.path.exists(filepath):
                messagebox.showerror("错误", f"文件不存在: {filepath}")
                return None
            
            # 加载文件
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证数据格式
            if not isinstance(data, dict) or 'actions' not in data:
                messagebox.showerror("错误", "文件格式不正确！")
                return None
            
            actions = data['actions']
            
            # 验证动作数据
            if not isinstance(actions, list):
                messagebox.showerror("错误", "动作数据格式不正确！")
                return None
            
            print(f"成功加载录制文件: {filepath}")
            print(f"动作数量: {len(actions)}")
            if actions:
                print(f"总时长: {actions[-1]['timestamp']:.2f}秒")
            
            messagebox.showinfo("成功", f"成功加载录制文件:\n{os.path.basename(filepath)}\n动作数量: {len(actions)}")
            return actions
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON文件格式错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return None
        except Exception as e:
            error_msg = f"加载文件时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return None
    
    def save_actions_as(self, actions: List[Dict[str, Any]]) -> bool:
        """
        另存为录制文件
        :param actions: 动作列表
        :return: 保存是否成功
        """
        try:
            if not actions:
                messagebox.showwarning("警告", "没有可保存的录制数据！")
                return False
            
            # 弹出保存对话框
            filepath = filedialog.asksaveasfilename(
                title="保存录制文件",
                initialdir=self.default_folder,
                defaultextension=".json",
                filetypes=[
                    ("JSON文件", "*.json"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not filepath:  # 用户取消了保存
                return False
            
            # 准备保存的数据
            save_data = {
                'version': '1.0',
                'created_time': datetime.now().isoformat(),
                'action_count': len(actions),
                'total_duration': actions[-1]['timestamp'] if actions else 0,
                'actions': actions
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            print(f"录制数据已保存到: {filepath}")
            messagebox.showinfo("成功", f"录制数据已保存到:\n{filepath}")
            return True
            
        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def get_recent_files(self, limit: int = 10) -> List[str]:
        """
        获取最近的录制文件列表
        :param limit: 返回文件数量限制
        :return: 文件路径列表
        """
        try:
            if not os.path.exists(self.default_folder):
                return []
            
            # 获取所有JSON文件
            files = []
            for filename in os.listdir(self.default_folder):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.default_folder, filename)
                    if os.path.isfile(filepath):
                        files.append(filepath)
            
            # 按修改时间排序（最新的在前）
            files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            return files[:limit]
            
        except Exception as e:
            print(f"获取最近文件列表时发生错误: {e}")
            return []
    
    def delete_recording(self, filepath: str) -> bool:
        """
        删除录制文件
        :param filepath: 文件路径
        :return: 删除是否成功
        """
        try:
            if not os.path.exists(filepath):
                messagebox.showerror("错误", "文件不存在！")
                return False
            
            # 确认删除
            result = messagebox.askyesno(
                "确认删除", 
                f"确定要删除录制文件吗？\n{os.path.basename(filepath)}"
            )
            
            if result:
                os.remove(filepath)
                messagebox.showinfo("成功", "文件已删除！")
                return True
            
            return False
            
        except Exception as e:
            error_msg = f"删除文件时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
