# 键盘鼠标动作录制和回放工具

## 项目简介
这是一个基于Python开发的键盘鼠标动作录制和回放工具，可以录制用户的键盘按键和鼠标操作，并能够准确地回放这些动作。

## 功能特性
- 🎯 录制键盘按键事件（按下和释放）
- 🖱️ 录制鼠标点击事件（左键、右键、中键）
- 📍 录制鼠标移动轨迹
- 🎡 录制鼠标滚轮滚动事件
- ⏰ 精确的时间戳记录
- 🔄 支持设置播放次数或无限循环
- 🎮 简洁的图形用户界面

## 技术栈
- Python 3.8.10
- pynput - 键盘鼠标事件处理
- tkinter - 图形用户界面
- JSON - 数据存储格式

## 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法
1. 运行主程序：`python main.py`
2. 点击"开始录制"按钮开始录制动作
3. 执行需要录制的键盘鼠标操作
4. 点击"停止录制"按钮结束录制
5. 设置播放次数（可选）
6. 点击"开始回放"按钮重现录制的动作

## 打包为可执行文件
```bash
pyinstaller --onefile --windowed main.py
```

## 项目结构
```
鼠标滚轮/
├── main.py              # 主程序入口
├── recorder.py          # 录制模块
├── player.py            # 回放模块
├── data_manager.py      # 数据存储管理
├── gui.py               # 图形用户界面
├── requirements.txt     # 依赖包列表
└── README.md           # 项目说明
```
