"""
键盘鼠标动作录制和回放工具 - 主程序
整合所有模块，提供完整的功能
"""

import sys
import os
from tkinter import messagebox
from recorder import ActionRecorder
from player import ActionPlayer
from data_manager import DataManager
from gui import MainGUI


class MouseKeyboardRecorderApp:
    """主应用程序类"""
    
    def __init__(self):
        # 初始化各个模块
        self.recorder = ActionRecorder()
        self.player = ActionPlayer()
        self.data_manager = DataManager()
        self.gui = MainGUI()
        
        # 设置回调函数
        self._setup_callbacks()
        
        # 当前录制的动作数据
        self.current_actions = []
        
    def _setup_callbacks(self):
        """设置各模块间的回调函数"""
        # GUI回调
        self.gui.on_start_recording = self._start_recording
        self.gui.on_stop_recording = self._stop_recording
        self.gui.on_start_playback = self._start_playback
        self.gui.on_stop_playback = self._stop_playback
        self.gui.on_save_actions = self._save_actions
        self.gui.on_load_actions = self._load_actions
        self.gui.on_save_as_actions = self._save_as_actions
        
        # 播放器回调
        self.player.on_play_complete = self._on_playback_complete
        self.player.on_play_stop = self._on_playback_stop
    
    def _start_recording(self) -> bool:
        """开始录制"""
        try:
            success = self.recorder.start_recording()
            if success:
                print("录制已开始")
            return success
        except Exception as e:
            error_msg = f"启动录制时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _stop_recording(self) -> bool:
        """停止录制"""
        try:
            success = self.recorder.stop_recording()
            if success:
                # 获取录制的动作
                self.current_actions = self.recorder.get_actions()
                print(f"录制完成，共 {len(self.current_actions)} 个动作")

                # 设置播放器的动作数据
                self.player.set_actions(self.current_actions)

                # 更新GUI显示详细统计信息
                self.gui.update_recording_stats(self.current_actions)

            return success
        except Exception as e:
            error_msg = f"停止录制时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _start_playback(self, repeat_count: int) -> bool:
        """开始回放"""
        try:
            if not self.current_actions:
                messagebox.showwarning("警告", "没有可回放的录制数据！")
                return False
                
            success = self.player.start_playback(repeat_count)
            if success:
                print(f"回放已开始，重复次数: {'无限' if repeat_count == -1 else repeat_count}")
            return success
        except Exception as e:
            error_msg = f"启动回放时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _stop_playback(self) -> bool:
        """停止回放"""
        try:
            success = self.player.stop_playback()
            if success:
                print("回放已停止")
            return success
        except Exception as e:
            error_msg = f"停止回放时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _save_actions(self) -> bool:
        """保存录制数据"""
        try:
            if not self.current_actions:
                messagebox.showwarning("警告", "没有可保存的录制数据！")
                return False
                
            return self.data_manager.save_actions(self.current_actions)
        except Exception as e:
            error_msg = f"保存数据时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _save_as_actions(self) -> bool:
        """另存为录制数据"""
        try:
            if not self.current_actions:
                messagebox.showwarning("警告", "没有可保存的录制数据！")
                return False
                
            return self.data_manager.save_actions_as(self.current_actions)
        except Exception as e:
            error_msg = f"保存数据时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _load_actions(self) -> bool:
        """加载录制数据"""
        try:
            actions = self.data_manager.load_actions()
            if actions is not None:
                self.current_actions = actions
                self.player.set_actions(self.current_actions)
                self.gui.set_actions_loaded(len(self.current_actions), self.current_actions)
                print(f"成功加载 {len(self.current_actions)} 个动作")
                return True
            return False
        except Exception as e:
            error_msg = f"加载数据时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return False
    
    def _on_playback_complete(self):
        """回放完成回调"""
        print("回放完成")
        self.gui.on_playback_complete()
    
    def _on_playback_stop(self):
        """回放停止回调"""
        print("回放已停止")
    
    def run(self):
        """运行应用程序"""
        try:
            print("键盘鼠标动作录制和回放工具启动")
            print("=" * 50)
            print("使用说明：")
            print("1. 点击'开始录制'按钮开始录制动作")
            print("2. 执行需要录制的键盘鼠标操作")
            print("3. 点击'停止录制'按钮结束录制")
            print("4. 设置播放次数（可选择无限循环）")
            print("5. 点击'开始回放'按钮重现录制的动作")
            print("6. 可以保存录制数据或加载已有的录制文件")
            print("=" * 50)
            
            # 运行GUI主循环
            self.gui.run()
            
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            error_msg = f"程序运行时发生错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            # 清理资源
            if self.recorder.is_recording:
                self.recorder.stop_recording()
            if self.player.is_playing:
                self.player.stop_playback()
            print("程序已退出")


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 6):
            print("错误: 需要Python 3.6或更高版本")
            sys.exit(1)
        
        # 创建并运行应用程序
        app = MouseKeyboardRecorderApp()
        app.run()
        
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
