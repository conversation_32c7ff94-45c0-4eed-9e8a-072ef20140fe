"""
键盘鼠标动作录制模块
负责捕获和记录键盘按键、鼠标点击、移动和滚轮事件
"""

import time
import threading
from pynput import mouse, keyboard
from typing import List, Dict, Any


class ActionRecorder:
    """动作录制器类"""
    
    def __init__(self):
        self.actions: List[Dict[str, Any]] = []
        self.is_recording = False
        self.start_time = 0
        
        # 监听器对象
        self.mouse_listener = None
        self.keyboard_listener = None
        
    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            return False
            
        self.actions.clear()
        self.is_recording = True
        self.start_time = time.time()
        
        # 启动鼠标监听器
        self.mouse_listener = mouse.Listener(
            on_move=self._on_mouse_move,
            on_click=self._on_mouse_click,
            on_scroll=self._on_mouse_scroll
        )
        
        # 启动键盘监听器
        self.keyboard_listener = keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )
        
        self.mouse_listener.start()
        self.keyboard_listener.start()
        
        print("开始录制...")
        return True
    
    def stop_recording(self):
        """停止录制"""
        if not self.is_recording:
            return False
            
        self.is_recording = False
        
        # 停止监听器
        if self.mouse_listener:
            self.mouse_listener.stop()
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            
        print(f"录制完成，共记录 {len(self.actions)} 个动作")
        return True
    
    def _get_timestamp(self) -> float:
        """获取相对时间戳"""
        return time.time() - self.start_time
    
    def _on_mouse_move(self, x, y):
        """鼠标移动事件处理"""
        if not self.is_recording:
            return
            
        action = {
            'type': 'mouse_move',
            'timestamp': self._get_timestamp(),
            'x': x,
            'y': y
        }
        self.actions.append(action)
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件处理"""
        if not self.is_recording:
            return
            
        action = {
            'type': 'mouse_click',
            'timestamp': self._get_timestamp(),
            'x': x,
            'y': y,
            'button': button.name,  # 'left', 'right', 'middle'
            'pressed': pressed  # True为按下，False为释放
        }
        self.actions.append(action)
    
    def _on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件处理"""
        if not self.is_recording:
            return
            
        action = {
            'type': 'mouse_scroll',
            'timestamp': self._get_timestamp(),
            'x': x,
            'y': y,
            'dx': dx,  # 水平滚动
            'dy': dy   # 垂直滚动
        }
        self.actions.append(action)
    
    def _on_key_press(self, key):
        """键盘按下事件处理"""
        if not self.is_recording:
            return
            
        try:
            # 处理普通字符键
            key_name = key.char
        except AttributeError:
            # 处理特殊键（如Ctrl, Alt, Shift等）
            key_name = key.name
            
        action = {
            'type': 'key_press',
            'timestamp': self._get_timestamp(),
            'key': key_name,
            'pressed': True
        }
        self.actions.append(action)
    
    def _on_key_release(self, key):
        """键盘释放事件处理"""
        if not self.is_recording:
            return
            
        try:
            key_name = key.char
        except AttributeError:
            key_name = key.name
            
        action = {
            'type': 'key_release',
            'timestamp': self._get_timestamp(),
            'key': key_name,
            'pressed': False
        }
        self.actions.append(action)
    
    def get_actions(self) -> List[Dict[str, Any]]:
        """获取录制的动作列表"""
        return self.actions.copy()
    
    def clear_actions(self):
        """清空动作列表"""
        self.actions.clear()
