"""
键盘鼠标动作录制模块
负责捕获和记录键盘按键、鼠标点击、移动和滚轮事件
"""

import time
import threading
import ctypes
from pynput import mouse, keyboard
from typing import List, Dict, Any


class ActionRecorder:
    """动作录制器类"""

    def __init__(self):
        self.actions: List[Dict[str, Any]] = []
        self.is_recording = False
        self.start_time = 0

        # 监听器对象
        self.mouse_listener = None
        self.keyboard_listener = None

        # DPI缩放信息
        self.dpi_scale_x, self.dpi_scale_y = self._get_dpi_scale()
        print(f"检测到DPI缩放: {self.dpi_scale_x:.2f} x {self.dpi_scale_y:.2f}")

    def _get_dpi_scale(self):
        """获取DPI缩放比例"""
        try:
            # 设置DPI感知
            ctypes.windll.shcore.SetProcessDpiAwareness(1)

            # 获取DPI信息
            user32 = ctypes.windll.user32
            hdc = user32.GetDC(0)
            dpi_x = ctypes.windll.gdi32.GetDeviceCaps(hdc, 88)  # LOGPIXELSX
            dpi_y = ctypes.windll.gdi32.GetDeviceCaps(hdc, 90)  # LOGPIXELSY
            user32.ReleaseDC(0, hdc)

            # 计算缩放比例
            scale_x = dpi_x / 96.0
            scale_y = dpi_y / 96.0

            return scale_x, scale_y
        except Exception as e:
            print(f"获取DPI信息失败，使用默认值: {e}")
            return 1.0, 1.0
        
    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            return False
            
        self.actions.clear()
        self.is_recording = True
        self.start_time = time.time()
        
        # 启动鼠标监听器
        self.mouse_listener = mouse.Listener(
            on_move=self._on_mouse_move,
            on_click=self._on_mouse_click,
            on_scroll=self._on_mouse_scroll
        )
        
        # 启动键盘监听器
        self.keyboard_listener = keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )
        
        self.mouse_listener.start()
        self.keyboard_listener.start()
        
        print("开始录制...")
        return True
    
    def stop_recording(self):
        """停止录制"""
        if not self.is_recording:
            return False
            
        self.is_recording = False
        
        # 停止监听器
        if self.mouse_listener:
            self.mouse_listener.stop()
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            
        print(f"录制完成，共记录 {len(self.actions)} 个动作")
        return True
    
    def _get_timestamp(self) -> float:
        """获取相对时间戳"""
        return time.time() - self.start_time
    
    def _on_mouse_move(self, x, y):
        """鼠标移动事件处理"""
        if not self.is_recording:
            return

        # 记录原始坐标（不进行DPI调整，因为pynput已经处理了）
        action = {
            'type': 'mouse_move',
            'timestamp': self._get_timestamp(),
            'x': int(x),
            'y': int(y),
            'dpi_scale_x': self.dpi_scale_x,  # 保存DPI信息用于回放
            'dpi_scale_y': self.dpi_scale_y
        }
        self.actions.append(action)
        # 调试信息（可选）
        if len(self.actions) % 50 == 0:  # 每50个移动事件打印一次
            print(f"录制移动: ({x}, {y}) [DPI: {self.dpi_scale_x:.2f}]")
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件处理"""
        if not self.is_recording:
            return

        action = {
            'type': 'mouse_click',
            'timestamp': self._get_timestamp(),
            'x': int(x),
            'y': int(y),
            'button': button.name,  # 'left', 'right', 'middle'
            'pressed': pressed,  # True为按下，False为释放
            'dpi_scale_x': self.dpi_scale_x,
            'dpi_scale_y': self.dpi_scale_y
        }
        self.actions.append(action)
        # 调试信息
        action_type = "按下" if pressed else "释放"
        print(f"录制点击: {button.name} {action_type} at ({x}, {y}) [DPI: {self.dpi_scale_x:.2f}]")
    
    def _on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件处理"""
        if not self.is_recording:
            return

        # 确保滚轮数据有效
        if dx == 0 and dy == 0:
            return  # 忽略无效的滚轮事件

        action = {
            'type': 'mouse_scroll',
            'timestamp': self._get_timestamp(),
            'x': int(x),
            'y': int(y),
            'dx': float(dx),  # 水平滚动，保持浮点精度
            'dy': float(dy),  # 垂直滚动，保持浮点精度
            'dpi_scale_x': self.dpi_scale_x,
            'dpi_scale_y': self.dpi_scale_y
        }
        self.actions.append(action)
        # 调试信息
        direction = "向上" if dy > 0 else "向下" if dy < 0 else "水平"
        print(f"录制滚轮: {direction} (dx={dx}, dy={dy}) at ({x}, {y}) [DPI: {self.dpi_scale_x:.2f}]")
    
    def _on_key_press(self, key):
        """键盘按下事件处理"""
        if not self.is_recording:
            return
            
        try:
            # 处理普通字符键
            key_name = key.char
        except AttributeError:
            # 处理特殊键（如Ctrl, Alt, Shift等）
            key_name = key.name
            
        action = {
            'type': 'key_press',
            'timestamp': self._get_timestamp(),
            'key': key_name,
            'pressed': True
        }
        self.actions.append(action)
    
    def _on_key_release(self, key):
        """键盘释放事件处理"""
        if not self.is_recording:
            return
            
        try:
            key_name = key.char
        except AttributeError:
            key_name = key.name
            
        action = {
            'type': 'key_release',
            'timestamp': self._get_timestamp(),
            'key': key_name,
            'pressed': False
        }
        self.actions.append(action)
    
    def get_actions(self) -> List[Dict[str, Any]]:
        """获取录制的动作列表"""
        return self.actions.copy()
    
    def clear_actions(self):
        """清空动作列表"""
        self.actions.clear()
