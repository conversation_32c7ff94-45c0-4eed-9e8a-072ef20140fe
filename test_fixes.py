"""
测试修复效果的脚本
专门测试鼠标位置和滚轮功能
"""

import time
import json
from recorder import ActionRecorder
from player import ActionPlayer


def test_mouse_recording():
    """测试鼠标录制功能"""
    print("=" * 60)
    print("测试鼠标录制功能")
    print("=" * 60)
    
    recorder = ActionRecorder()
    
    print("开始录制 8 秒，请执行以下操作：")
    print("1. 移动鼠标到不同位置")
    print("2. 点击鼠标左键几次")
    print("3. 滚动鼠标滚轮（向上和向下）")
    print("4. 点击鼠标右键")
    print("5. 再次滚动滚轮")
    print("-" * 60)
    
    # 开始录制
    recorder.start_recording()
    time.sleep(8)
    recorder.stop_recording()
    
    actions = recorder.get_actions()
    print(f"\n录制完成！共捕获 {len(actions)} 个动作")
    
    # 分析录制结果
    analyze_actions(actions)
    
    return actions


def analyze_actions(actions):
    """分析动作数据"""
    if not actions:
        print("没有录制到任何动作")
        return
    
    # 统计各类动作
    stats = {
        'mouse_move': 0,
        'mouse_click': 0,
        'mouse_scroll': 0,
        'key_press': 0,
        'key_release': 0
    }
    
    scroll_events = []
    click_events = []
    
    for action in actions:
        action_type = action['type']
        stats[action_type] = stats.get(action_type, 0) + 1
        
        if action_type == 'mouse_scroll':
            scroll_events.append(action)
        elif action_type == 'mouse_click':
            click_events.append(action)
    
    print("\n=== 动作统计 ===")
    print(f"鼠标移动: {stats['mouse_move']} 次")
    print(f"鼠标点击: {stats['mouse_click']} 次")
    print(f"滚轮操作: {stats['mouse_scroll']} 次")
    print(f"键盘按下: {stats['key_press']} 次")
    print(f"键盘释放: {stats['key_release']} 次")
    
    # 详细分析滚轮事件
    if scroll_events:
        print(f"\n=== 滚轮事件详情 ===")
        for i, event in enumerate(scroll_events):
            direction = "向上" if event['dy'] > 0 else "向下" if event['dy'] < 0 else "水平"
            print(f"{i+1}. {direction} (dx={event['dx']}, dy={event['dy']}) at ({event['x']}, {event['y']})")
    else:
        print("\n⚠️  警告：没有检测到滚轮事件！")
    
    # 分析点击事件
    if click_events:
        print(f"\n=== 点击事件详情 ===")
        for i, event in enumerate(click_events):
            action = "按下" if event['pressed'] else "释放"
            print(f"{i+1}. {event['button']} {action} at ({event['x']}, {event['y']})")
    
    # 坐标范围分析
    if actions:
        x_coords = [a['x'] for a in actions if 'x' in a]
        y_coords = [a['y'] for a in actions if 'y' in a]
        
        if x_coords and y_coords:
            print(f"\n=== 坐标范围 ===")
            print(f"X 坐标: {min(x_coords)} ~ {max(x_coords)}")
            print(f"Y 坐标: {min(y_coords)} ~ {max(y_coords)}")
    
    # 时间分析
    if actions:
        total_time = actions[-1]['timestamp']
        print(f"\n=== 时间信息 ===")
        print(f"总录制时间: {total_time:.2f} 秒")
        print(f"平均事件间隔: {total_time/len(actions):.3f} 秒")


def test_mouse_playback(actions):
    """测试鼠标回放功能"""
    if not actions:
        print("没有动作可以回放")
        return
    
    print("\n" + "=" * 60)
    print("测试鼠标回放功能")
    print("=" * 60)
    
    player = ActionPlayer()
    player.set_actions(actions)
    
    print(f"准备回放 {len(actions)} 个动作...")
    print("3秒后开始回放，请观察：")
    print("1. 鼠标是否移动到正确位置")
    print("2. 点击是否在正确位置执行")
    print("3. 滚轮是否正常工作")
    print("-" * 60)
    
    time.sleep(3)
    
    # 开始回放
    player.start_playback(1)
    
    # 等待回放完成
    while player.is_playing:
        time.sleep(0.1)
    
    print("回放完成！")


def save_test_data(actions, filename="test_recording.json"):
    """保存测试数据"""
    if not actions:
        return
    
    test_data = {
        'version': '1.0',
        'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'action_count': len(actions),
        'total_duration': actions[-1]['timestamp'] if actions else 0,
        'actions': actions
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n测试数据已保存到: {filename}")


def main():
    """主测试函数"""
    print("鼠标功能修复测试")
    print("此测试将验证鼠标位置和滚轮功能是否正常工作")
    
    try:
        # 测试录制
        actions = test_mouse_recording()
        
        # 保存测试数据
        save_test_data(actions)
        
        # 询问是否测试回放
        if actions:
            response = input("\n是否要测试回放功能？(y/n): ").lower().strip()
            if response == 'y':
                test_mouse_playback(actions)
        
        print("\n" + "=" * 60)
        print("测试完成！")
        
        # 给出结论
        scroll_count = sum(1 for a in actions if a['type'] == 'mouse_scroll')
        if scroll_count > 0:
            print("✓ 滚轮功能正常 - 检测到滚轮事件")
        else:
            print("✗ 滚轮功能异常 - 未检测到滚轮事件")
        
        click_count = sum(1 for a in actions if a['type'] == 'mouse_click')
        if click_count > 0:
            print("✓ 鼠标点击功能正常")
        else:
            print("? 未测试鼠标点击功能")
        
        move_count = sum(1 for a in actions if a['type'] == 'mouse_move')
        if move_count > 0:
            print("✓ 鼠标移动功能正常")
        else:
            print("? 未测试鼠标移动功能")
        
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
