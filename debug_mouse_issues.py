"""
鼠标问题诊断脚本
用于测试和调试鼠标位置和滚轮问题
"""

import time
import json
from pynput import mouse, keyboard
from pynput.mouse import Button
import threading


class MouseDebugger:
    """鼠标调试器"""
    
    def __init__(self):
        self.events = []
        self.is_recording = False
        self.mouse_controller = mouse.Controller()
        
    def start_debug_recording(self, duration=10):
        """开始调试录制"""
        print(f"开始录制 {duration} 秒，请进行以下操作：")
        print("1. 移动鼠标到不同位置")
        print("2. 点击鼠标左键")
        print("3. 滚动鼠标滚轮（向上和向下）")
        print("4. 点击鼠标右键")
        print("-" * 50)
        
        self.events.clear()
        self.is_recording = True
        start_time = time.time()
        
        # 启动监听器
        mouse_listener = mouse.Listener(
            on_move=self._on_move,
            on_click=self._on_click,
            on_scroll=self._on_scroll
        )
        
        mouse_listener.start()
        
        # 录制指定时间
        time.sleep(duration)
        
        self.is_recording = False
        mouse_listener.stop()
        
        print(f"\n录制完成！共捕获 {len(self.events)} 个事件")
        return self.events
    
    def _on_move(self, x, y):
        """鼠标移动事件"""
        if not self.is_recording:
            return
            
        event = {
            'type': 'move',
            'timestamp': time.time(),
            'x': x,
            'y': y
        }
        self.events.append(event)
        print(f"移动: ({x}, {y})")
    
    def _on_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if not self.is_recording:
            return
            
        event = {
            'type': 'click',
            'timestamp': time.time(),
            'x': x,
            'y': y,
            'button': button.name,
            'pressed': pressed
        }
        self.events.append(event)
        action = "按下" if pressed else "释放"
        print(f"点击: {button.name} {action} at ({x}, {y})")
    
    def _on_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件"""
        if not self.is_recording:
            return
            
        event = {
            'type': 'scroll',
            'timestamp': time.time(),
            'x': x,
            'y': y,
            'dx': dx,
            'dy': dy
        }
        self.events.append(event)
        direction = "向上" if dy > 0 else "向下" if dy < 0 else "水平"
        print(f"滚轮: {direction} (dx={dx}, dy={dy}) at ({x}, {y})")
    
    def test_playback(self, events):
        """测试回放功能"""
        if not events:
            print("没有事件可以回放")
            return
            
        print(f"\n开始回放 {len(events)} 个事件...")
        print("3秒后开始回放，请准备观察...")
        time.sleep(3)
        
        # 获取当前鼠标位置作为参考
        current_pos = self.mouse_controller.position
        print(f"当前鼠标位置: {current_pos}")
        
        start_time = events[0]['timestamp']
        
        for i, event in enumerate(events):
            # 计算延迟
            if i > 0:
                delay = event['timestamp'] - events[i-1]['timestamp']
                if delay > 0:
                    time.sleep(min(delay, 0.1))  # 限制最大延迟
            
            try:
                if event['type'] == 'move':
                    self.mouse_controller.position = (event['x'], event['y'])
                    print(f"回放移动: ({event['x']}, {event['y']})")
                    
                elif event['type'] == 'click':
                    self.mouse_controller.position = (event['x'], event['y'])
                    button = getattr(Button, event['button'])
                    
                    if event['pressed']:
                        self.mouse_controller.press(button)
                        print(f"回放点击: {event['button']} 按下 at ({event['x']}, {event['y']})")
                    else:
                        self.mouse_controller.release(button)
                        print(f"回放点击: {event['button']} 释放 at ({event['x']}, {event['y']})")
                        
                elif event['type'] == 'scroll':
                    self.mouse_controller.position = (event['x'], event['y'])
                    self.mouse_controller.scroll(event['dx'], event['dy'])
                    direction = "向上" if event['dy'] > 0 else "向下" if event['dy'] < 0 else "水平"
                    print(f"回放滚轮: {direction} (dx={event['dx']}, dy={event['dy']}) at ({event['x']}, {event['y']})")
                    
            except Exception as e:
                print(f"回放事件时出错: {e}")
        
        print("回放完成！")
    
    def analyze_events(self, events):
        """分析事件"""
        if not events:
            print("没有事件可分析")
            return
            
        print(f"\n=== 事件分析 ===")
        print(f"总事件数: {len(events)}")
        
        # 按类型统计
        type_counts = {}
        for event in events:
            event_type = event['type']
            type_counts[event_type] = type_counts.get(event_type, 0) + 1
        
        print("事件类型统计:")
        for event_type, count in type_counts.items():
            print(f"  {event_type}: {count}")
        
        # 分析滚轮事件
        scroll_events = [e for e in events if e['type'] == 'scroll']
        if scroll_events:
            print(f"\n滚轮事件详情:")
            for i, event in enumerate(scroll_events):
                print(f"  {i+1}. dx={event['dx']}, dy={event['dy']} at ({event['x']}, {event['y']})")
        else:
            print("\n⚠️  没有检测到滚轮事件！这可能是问题所在。")
        
        # 分析坐标范围
        move_events = [e for e in events if e['type'] in ['move', 'click', 'scroll']]
        if move_events:
            x_coords = [e['x'] for e in move_events]
            y_coords = [e['y'] for e in move_events]
            print(f"\n坐标范围:")
            print(f"  X: {min(x_coords)} ~ {max(x_coords)}")
            print(f"  Y: {min(y_coords)} ~ {max(y_coords)}")
    
    def save_debug_data(self, events, filename="debug_events.json"):
        """保存调试数据"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(events, f, indent=2, ensure_ascii=False)
        print(f"调试数据已保存到: {filename}")


def main():
    """主函数"""
    print("=" * 60)
    print("鼠标问题诊断工具")
    print("=" * 60)
    
    debugger = MouseDebugger()
    
    # 获取当前鼠标位置
    current_pos = debugger.mouse_controller.position
    print(f"当前鼠标位置: {current_pos}")
    
    # 录制测试
    events = debugger.start_debug_recording(10)
    
    # 分析事件
    debugger.analyze_events(events)
    
    # 保存调试数据
    debugger.save_debug_data(events)
    
    # 询问是否回放
    response = input("\n是否要测试回放功能？(y/n): ").lower().strip()
    if response == 'y':
        debugger.test_playback(events)
    
    print("\n诊断完成！")


if __name__ == "__main__":
    main()
