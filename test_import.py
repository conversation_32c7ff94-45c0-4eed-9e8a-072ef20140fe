"""
测试模块导入
"""

import sys
print(f"Python 版本: {sys.version}")
print(f"Python 路径: {sys.executable}")

try:
    import pynput
    print("✓ pynput 导入成功")

    from pynput import mouse, keyboard
    print("✓ pynput.mouse 和 pynput.keyboard 导入成功")

    # 测试创建控制器
    mouse_controller = mouse.Controller()
    keyboard_controller = keyboard.Controller()
    print("✓ 鼠标和键盘控制器创建成功")

    print(f"当前鼠标位置: {mouse_controller.position}")

    # 测试监听器
    def test_listener():
        listener = mouse.Listener(on_move=lambda x, y: None)
        print("✓ 鼠标监听器创建成功")
        return True

    test_listener()

except ImportError as e:
    print(f"✗ pynput 导入失败: {e}")
except Exception as e:
    print(f"✗ 其他错误: {e}")

try:
    import tkinter as tk
    print("✓ tkinter 导入成功")
except ImportError as e:
    print(f"✗ tkinter 导入失败: {e}")

print("\n模块导入测试完成")
