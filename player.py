"""
动作回放模块
负责按照录制的时序重现键盘鼠标动作
"""

import time
import threading
import ctypes
from typing import List, Dict, Any, Callable
from pynput import mouse, keyboard
from pynput.mouse import Button
from pynput.keyboard import Key


class ActionPlayer:
    """动作回放器类"""

    def __init__(self):
        self.actions: List[Dict[str, Any]] = []
        self.is_playing = False
        self.play_thread = None
        self.stop_event = threading.Event()

        # 控制器对象
        self.mouse_controller = mouse.Controller()
        self.keyboard_controller = keyboard.Controller()

        # 回调函数
        self.on_play_start: Callable = None
        self.on_play_complete: Callable = None
        self.on_play_stop: Callable = None

        # DPI缩放信息
        self.current_dpi_scale_x, self.current_dpi_scale_y = self._get_current_dpi_scale()
        print(f"播放器DPI缩放: {self.current_dpi_scale_x:.2f} x {self.current_dpi_scale_y:.2f}")

    def _get_current_dpi_scale(self):
        """获取当前DPI缩放比例"""
        try:
            # 设置DPI感知
            ctypes.windll.shcore.SetProcessDpiAwareness(1)

            # 获取DPI信息
            user32 = ctypes.windll.user32
            hdc = user32.GetDC(0)
            dpi_x = ctypes.windll.gdi32.GetDeviceCaps(hdc, 88)  # LOGPIXELSX
            dpi_y = ctypes.windll.gdi32.GetDeviceCaps(hdc, 90)  # LOGPIXELSY
            user32.ReleaseDC(0, hdc)

            # 计算缩放比例
            scale_x = dpi_x / 96.0
            scale_y = dpi_y / 96.0

            return scale_x, scale_y
        except Exception as e:
            print(f"获取DPI信息失败，使用默认值: {e}")
            return 1.0, 1.0
    
    def set_actions(self, actions: List[Dict[str, Any]]):
        """设置要回放的动作列表"""
        self.actions = actions.copy()
    
    def start_playback(self, repeat_count: int = 1):
        """
        开始回放
        :param repeat_count: 重复次数，-1表示无限循环
        """
        if self.is_playing or not self.actions:
            return False
            
        self.is_playing = True
        self.stop_event.clear()
        
        # 在新线程中执行回放
        self.play_thread = threading.Thread(
            target=self._play_actions,
            args=(repeat_count,),
            daemon=True
        )
        self.play_thread.start()
        
        if self.on_play_start:
            self.on_play_start()
            
        return True
    
    def stop_playback(self):
        """停止回放"""
        if not self.is_playing:
            return False
            
        self.stop_event.set()
        self.is_playing = False
        
        if self.on_play_stop:
            self.on_play_stop()
            
        return True
    
    def _play_actions(self, repeat_count: int):
        """执行动作回放的主循环"""
        try:
            current_repeat = 0
            
            while (repeat_count == -1 or current_repeat < repeat_count) and not self.stop_event.is_set():
                print(f"开始第 {current_repeat + 1} 次回放...")
                
                if not self._play_single_sequence():
                    break
                    
                current_repeat += 1
                
                # 如果不是无限循环且不是最后一次，稍作延迟
                if repeat_count != -1 and current_repeat < repeat_count:
                    time.sleep(0.5)
                    
        except Exception as e:
            print(f"回放过程中发生错误: {e}")
        finally:
            self.is_playing = False
            if self.on_play_complete:
                self.on_play_complete()
    
    def _play_single_sequence(self) -> bool:
        """执行单次动作序列回放"""
        if not self.actions:
            return False
            
        start_time = time.time()
        last_timestamp = 0
        
        for action in self.actions:
            if self.stop_event.is_set():
                return False
                
            # 计算需要等待的时间
            current_timestamp = action['timestamp']
            wait_time = current_timestamp - last_timestamp
            
            if wait_time > 0:
                time.sleep(wait_time)
                
            # 执行动作
            self._execute_action(action)
            last_timestamp = current_timestamp
            
        return True
    
    def _execute_action(self, action: Dict[str, Any]):
        """执行单个动作"""
        action_type = action['type']
        
        try:
            if action_type == 'mouse_move':
                self._execute_mouse_move(action)
            elif action_type == 'mouse_click':
                self._execute_mouse_click(action)
            elif action_type == 'mouse_scroll':
                self._execute_mouse_scroll(action)
            elif action_type == 'key_press':
                self._execute_key_press(action)
            elif action_type == 'key_release':
                self._execute_key_release(action)
                
        except Exception as e:
            print(f"执行动作时发生错误: {action_type}, {e}")
    
    def _execute_mouse_move(self, action: Dict[str, Any]):
        """执行鼠标移动"""
        # 获取原始坐标
        original_x, original_y = int(action['x']), int(action['y'])

        # 获取录制时的DPI缩放（如果有的话）
        recorded_scale_x = action.get('dpi_scale_x', 1.0)
        recorded_scale_y = action.get('dpi_scale_y', 1.0)

        # 如果录制时和回放时的DPI不同，需要调整坐标
        if recorded_scale_x != self.current_dpi_scale_x or recorded_scale_y != self.current_dpi_scale_y:
            # 计算调整后的坐标
            adjusted_x = int(original_x * self.current_dpi_scale_x / recorded_scale_x)
            adjusted_y = int(original_y * self.current_dpi_scale_y / recorded_scale_y)
            print(f"DPI调整: ({original_x}, {original_y}) -> ({adjusted_x}, {adjusted_y})")
        else:
            adjusted_x, adjusted_y = original_x, original_y

        self.mouse_controller.position = (adjusted_x, adjusted_y)

        # 调试信息（减少输出频率）
        if hasattr(self, '_move_count'):
            self._move_count += 1
        else:
            self._move_count = 1
        if self._move_count % 50 == 0:
            print(f"回放移动: ({adjusted_x}, {adjusted_y})")
    
    def _execute_mouse_click(self, action: Dict[str, Any]):
        """执行鼠标点击"""
        # 获取原始坐标
        original_x, original_y = int(action['x']), int(action['y'])

        # 获取录制时的DPI缩放（如果有的话）
        recorded_scale_x = action.get('dpi_scale_x', 1.0)
        recorded_scale_y = action.get('dpi_scale_y', 1.0)

        # 如果录制时和回放时的DPI不同，需要调整坐标
        if recorded_scale_x != self.current_dpi_scale_x or recorded_scale_y != self.current_dpi_scale_y:
            adjusted_x = int(original_x * self.current_dpi_scale_x / recorded_scale_x)
            adjusted_y = int(original_y * self.current_dpi_scale_y / recorded_scale_y)
        else:
            adjusted_x, adjusted_y = original_x, original_y

        # 设置鼠标位置
        self.mouse_controller.position = (adjusted_x, adjusted_y)

        # 获取按钮类型
        button_name = action['button']
        if button_name == 'left':
            button = Button.left
        elif button_name == 'right':
            button = Button.right
        elif button_name == 'middle':
            button = Button.middle
        else:
            print(f"未知按钮类型: {button_name}")
            return

        # 执行按下或释放
        if action['pressed']:
            self.mouse_controller.press(button)
            print(f"回放点击: {button_name} 按下 at ({adjusted_x}, {adjusted_y})")
        else:
            self.mouse_controller.release(button)
            print(f"回放点击: {button_name} 释放 at ({adjusted_x}, {adjusted_y})")
    
    def _execute_mouse_scroll(self, action: Dict[str, Any]):
        """执行鼠标滚轮滚动"""
        # 获取原始坐标
        original_x, original_y = int(action['x']), int(action['y'])
        dx, dy = float(action['dx']), float(action['dy'])

        # 获取录制时的DPI缩放（如果有的话）
        recorded_scale_x = action.get('dpi_scale_x', 1.0)
        recorded_scale_y = action.get('dpi_scale_y', 1.0)

        # 如果录制时和回放时的DPI不同，需要调整坐标
        if recorded_scale_x != self.current_dpi_scale_x or recorded_scale_y != self.current_dpi_scale_y:
            adjusted_x = int(original_x * self.current_dpi_scale_x / recorded_scale_x)
            adjusted_y = int(original_y * self.current_dpi_scale_y / recorded_scale_y)
        else:
            adjusted_x, adjusted_y = original_x, original_y

        # 设置鼠标位置
        self.mouse_controller.position = (adjusted_x, adjusted_y)

        # 执行滚轮操作
        try:
            self.mouse_controller.scroll(dx, dy)
            direction = "向上" if dy > 0 else "向下" if dy < 0 else "水平"
            print(f"回放滚轮: {direction} (dx={dx}, dy={dy}) at ({adjusted_x}, {adjusted_y})")
        except Exception as e:
            print(f"滚轮回放失败: {e}")
    
    def _execute_key_press(self, action: Dict[str, Any]):
        """执行键盘按下"""
        key = self._get_key_object(action['key'])
        if key:
            self.keyboard_controller.press(key)
    
    def _execute_key_release(self, action: Dict[str, Any]):
        """执行键盘释放"""
        key = self._get_key_object(action['key'])
        if key:
            self.keyboard_controller.release(key)
    
    def _get_key_object(self, key_name: str):
        """将键名转换为pynput的Key对象"""
        if key_name is None:
            return None
            
        # 处理普通字符
        if len(key_name) == 1:
            return key_name
            
        # 处理特殊键
        special_keys = {
            'space': Key.space,
            'enter': Key.enter,
            'tab': Key.tab,
            'shift': Key.shift,
            'shift_l': Key.shift_l,
            'shift_r': Key.shift_r,
            'ctrl': Key.ctrl,
            'ctrl_l': Key.ctrl_l,
            'ctrl_r': Key.ctrl_r,
            'alt': Key.alt,
            'alt_l': Key.alt_l,
            'alt_r': Key.alt_r,
            'cmd': Key.cmd,
            'cmd_l': Key.cmd_l,
            'cmd_r': Key.cmd_r,
            'esc': Key.esc,
            'f1': Key.f1, 'f2': Key.f2, 'f3': Key.f3, 'f4': Key.f4,
            'f5': Key.f5, 'f6': Key.f6, 'f7': Key.f7, 'f8': Key.f8,
            'f9': Key.f9, 'f10': Key.f10, 'f11': Key.f11, 'f12': Key.f12,
            'up': Key.up, 'down': Key.down, 'left': Key.left, 'right': Key.right,
            'home': Key.home, 'end': Key.end, 'page_up': Key.page_up, 'page_down': Key.page_down,
            'insert': Key.insert, 'delete': Key.delete, 'backspace': Key.backspace
        }
        
        return special_keys.get(key_name.lower(), key_name)
