"""
打包脚本
使用PyInstaller将程序打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False

def check_pathlib_conflict():
    """检查并解决pathlib包冲突"""
    try:
        # 检查是否安装了冲突的pathlib包
        result = subprocess.run([sys.executable, '-m', 'pip', 'show', 'pathlib'],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("⚠️  检测到冲突的pathlib包")
            print("pathlib包与PyInstaller不兼容，需要卸载...")

            # 自动卸载pathlib包
            uninstall_result = subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'pathlib', '-y'],
                                            capture_output=True, text=True)

            if uninstall_result.returncode == 0:
                print("✓ 已成功卸载冲突的pathlib包")
                return True
            else:
                print("✗ 卸载pathlib包失败")
                print("请手动运行: pip uninstall pathlib")
                return False
        else:
            print("✓ 未检测到pathlib包冲突")
            return True

    except Exception as e:
        print(f"检查pathlib冲突时发生错误: {e}")
        return True  # 继续执行，让PyInstaller自己报错

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        print(f"删除文件: {spec_file}")
        spec_file.unlink()

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pynput.mouse._win32',
        'pynput.keyboard._win32',
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        'tkinter.messagebox'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MouseKeyboardRecorder',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以在这里指定图标文件
)
'''
    
    with open('MouseKeyboardRecorder.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建PyInstaller配置文件: MouseKeyboardRecorder.spec")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', 'MouseKeyboardRecorder.spec']
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功！")
            
            # 检查输出文件
            exe_path = os.path.join('dist', 'MouseKeyboardRecorder.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ 可执行文件已生成: {exe_path}")
                print(f"✓ 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("✗ 可执行文件未找到")
                return False
        else:
            print("✗ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建过程中发生错误: {e}")
        return False

def create_batch_files():
    """创建便捷的批处理文件"""
    
    # 创建安装依赖的批处理文件
    install_bat = '''@echo off
echo 安装项目依赖包...
pip install -r requirements.txt
if %errorlevel% equ 0 (
    echo 依赖包安装成功！
    echo 现在可以运行: python main.py
) else (
    echo 依赖包安装失败，请检查网络连接和Python环境
)
pause
'''
    
    with open('install_requirements.bat', 'w', encoding='gbk') as f:
        f.write(install_bat)
    
    # 创建运行程序的批处理文件
    run_bat = '''@echo off
echo 启动键盘鼠标录制工具...
python main.py
if %errorlevel% neq 0 (
    echo 程序运行出错，请确保已安装依赖包
    echo 运行 install_requirements.bat 安装依赖
    pause
)
'''
    
    with open('run_program.bat', 'w', encoding='gbk') as f:
        f.write(run_bat)
    
    # 创建构建可执行文件的批处理文件
    build_bat = '''@echo off
echo 构建可执行文件...
python build.py
pause
'''
    
    with open('build_exe.bat', 'w', encoding='gbk') as f:
        f.write(build_bat)
    
    print("✓ 已创建批处理文件:")
    print("  - install_requirements.bat (安装依赖)")
    print("  - run_program.bat (运行程序)")
    print("  - build_exe.bat (构建可执行文件)")

def main():
    """主函数"""
    print("=" * 60)
    print("键盘鼠标录制工具 - 打包脚本")
    print("=" * 60)

    # 检查PyInstaller
    if not check_pyinstaller():
        return

    # 检查pathlib冲突
    print("\n1. 检查包冲突...")
    if not check_pathlib_conflict():
        return

    # 清理构建目录
    print("\n2. 清理构建目录...")
    clean_build_dirs()

    # 创建spec文件
    print("\n3. 创建配置文件...")
    create_spec_file()

    # 构建可执行文件
    print("\n4. 构建可执行文件...")
    success = build_executable()

    # 创建批处理文件
    print("\n5. 创建批处理文件...")
    create_batch_files()

    print("\n" + "=" * 60)
    if success:
        print("✓ 打包完成！")
        print("\n生成的文件:")
        print("  - dist/MouseKeyboardRecorder.exe (可执行文件)")
        print("  - install_requirements.bat (安装依赖)")
        print("  - run_program.bat (运行源码)")
        print("  - build_exe.bat (重新构建)")
        print("\n使用说明:")
        print("  1. 直接运行 MouseKeyboardRecorder.exe")
        print("  2. 或者先运行 install_requirements.bat 安装依赖，再运行 run_program.bat")
    else:
        print("✗ 打包失败，请检查错误信息")
        print("\n常见问题解决:")
        print("  - 如果遇到pathlib冲突: pip uninstall pathlib")
        print("  - 如果遇到权限问题: 以管理员身份运行")
        print("  - 如果遇到模块缺失: pip install -r requirements.txt")
    print("=" * 60)

if __name__ == "__main__":
    main()
